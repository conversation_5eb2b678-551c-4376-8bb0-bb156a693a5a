# @aws-sdk/crt-loader

[![NPM version](https://img.shields.io/npm/v/@aws-sdk/crt-loader/latest.svg)](https://www.npmjs.com/package/@aws-sdk/crt-loader)
[![NPM downloads](https://img.shields.io/npm/dm/@aws-sdk/crt-loader.svg)](https://www.npmjs.com/package/@aws-sdk/crt-loader)

This internal package is a loader for AWS Common Runtime (CRT) native modules that only executable in Node.js runtime.
See also https://github.com/aws/aws-sdk-js-v3/tree/main#functionality-requiring-aws-common-runtime-crt.
