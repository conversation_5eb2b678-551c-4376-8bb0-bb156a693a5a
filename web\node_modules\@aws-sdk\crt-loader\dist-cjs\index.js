"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
module.exports = __toCommonJS(index_exports);
var import_util_user_agent_node = require("@aws-sdk/util-user-agent-node");
__reExport(index_exports, require("aws-crt"), module.exports);
import_util_user_agent_node.crtAvailability.isCrtAvailable = true;
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  auth,
  cancel,
  checksums,
  crypto,
  crt,
  eventstream,
  http,
  io,
  iot,
  mqtt,
  mqtt5,
  mqtt_request_response,
  platform,
  promise,
  resource_safety,
  CrtError
});

