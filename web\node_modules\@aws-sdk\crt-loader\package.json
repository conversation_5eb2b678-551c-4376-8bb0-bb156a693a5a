{"name": "@aws-sdk/crt-loader", "version": "3.840.0", "description": "Loader for AWS Common Runtime https://github.com/awslabs/aws-crt-nodejs", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "node ../../scripts/compilation/inline crt-loader", "build:es": "tsc -p tsconfig.es.json", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "extract:docs": "api-extractor run --local"}, "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@aws-sdk/util-user-agent-node": "3.840.0", "aws-crt": "^1.24.0", "tslib": "^2.6.2"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typescript": "~5.8.3"}, "engines": {"node": ">=18.0.0"}, "files": ["dist-*/**"], "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/packages/crt-loader", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "packages/crt-loader"}}