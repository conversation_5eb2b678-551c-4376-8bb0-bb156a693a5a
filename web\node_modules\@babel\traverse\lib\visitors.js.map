{"version": 3, "names": ["virtualTypes", "require", "virtualTypesValidators", "_t", "_context", "DEPRECATED_KEYS", "DEPRECATED_ALIASES", "FLIPPED_ALIAS_KEYS", "TYPES", "__internal__deprecationWarning", "deprecationWarning", "isVirtualType", "type", "isExplodedVisitor", "visitor", "_exploded", "explode$1", "nodeType", "Object", "keys", "should<PERSON><PERSON>re<PERSON>ey", "parts", "split", "length", "fns", "part", "verify$1", "__esModule", "ensureEntranceObjects", "ensureCallbackArrays", "wrapCheck", "types", "mergePair", "aliases", "deprecated<PERSON>ey", "depre<PERSON><PERSON><PERSON><PERSON>", "alias", "existing", "assign", "_verified", "Error", "validateVisitorMethods", "includes", "visitors", "<PERSON><PERSON><PERSON>", "path", "val", "concat", "fn", "TypeError", "merge", "states", "wrapper", "mergedVisitor", "defineProperty", "enumerable", "i", "state", "topVisitor", "wrapWithStateOrWrapper", "key", "typeVisitor", "nodeVisitor", "oldVisitor", "newVisitor", "phase", "Array", "isArray", "map", "newFn", "call", "toString", "obj", "enter", "exit", "fnKey", "validator", "apply", "arguments", "dest", "src", "_environmentVisitor", "FunctionParent", "isArrowFunctionExpression", "skip", "isMethod", "requeueComputedKeyAndDecorators", "Property", "isObjectProperty", "environmentVisitor"], "sources": ["../src/visitors.ts"], "sourcesContent": ["import * as virtualTypes from \"./path/lib/virtual-types.ts\";\nimport * as virtualTypesValidators from \"./path/lib/virtual-types-validator.ts\";\nimport type { Node } from \"@babel/types\";\nimport {\n  DEPRECATED_KEYS,\n  DEPRECATED_ALIASES,\n  FLIPPED_ALIAS_KEYS,\n  TYPES,\n  __internal__deprecationWarning as deprecationWarning,\n} from \"@babel/types\";\nimport type { ExplodedVisitor, NodePath, Visitor } from \"./index.ts\";\nimport type { ExplVisitNode, VisitNodeFunction, VisitPhase } from \"./types.ts\";\nimport { requeueComputedKeyAndDecorators } from \"./path/context.ts\";\n\ntype VIRTUAL_TYPES = keyof typeof virtualTypes;\nfunction isVirtualType(type: string): type is VIRTUAL_TYPES {\n  return type in virtualTypes;\n}\nexport type VisitWrapper<S = any> = (\n  stateName: string | undefined,\n  visitorType: VisitPhase,\n  callback: VisitNodeFunction<S, Node>,\n) => VisitNodeFunction<S, Node>;\n\nexport function isExplodedVisitor(\n  visitor: Visitor,\n): visitor is ExplodedVisitor {\n  // @ts-expect-error _exploded is not defined on non-exploded Visitor\n  return visitor?._exploded;\n}\n\n// We need to name this function `explode$1` because otherwise rollup-plugin-dts\n// will generate a `namespace traverse { var explode: typeof explode; }` when\n// bundling @babel/traverse's index.d.ts.\n// TODO: Just call it `explode` once https://github.com/Swatinem/rollup-plugin-dts/issues/307\n// is fixed.\nexport { explode$1 as explode };\n/**\n * explode() will take a visitor object with all of the various shorthands\n * that we support, and validates & normalizes it into a common format, ready\n * to be used in traversal\n *\n * The various shorthands are:\n * * `Identifier() { ... }` -> `Identifier: { enter() { ... } }`\n * * `\"Identifier|NumericLiteral\": { ... }` -> `Identifier: { ... }, NumericLiteral: { ... }`\n * * Aliases in `@babel/types`: e.g. `Property: { ... }` -> `ObjectProperty: { ... }, ClassProperty: { ... }`\n * Other normalizations are:\n * * Visitors of virtual types are wrapped, so that they are only visited when\n *   their dynamic check passes\n * * `enter` and `exit` functions are wrapped in arrays, to ease merging of\n *   visitors\n */\nfunction explode$1<S>(visitor: Visitor<S>): ExplodedVisitor<S> {\n  if (isExplodedVisitor(visitor)) return visitor;\n  // @ts-expect-error `visitor` will be cast to ExplodedVisitor by this function\n  visitor._exploded = true;\n\n  // normalise pipes\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    const parts: Array<string> = nodeType.split(\"|\");\n    if (parts.length === 1) continue;\n\n    const fns = visitor[nodeType];\n    delete visitor[nodeType];\n\n    for (const part of parts) {\n      // @ts-expect-error part will be verified by `verify` later\n      visitor[part] = fns;\n    }\n  }\n\n  // verify data structure\n  verify$1(visitor);\n\n  // make sure there's no __esModule type since this is because we're using loose mode\n  // and it sets __esModule to be enumerable on all modules :(\n  // @ts-expect-error ESModule interop\n  delete visitor.__esModule;\n\n  // ensure visitors are objects\n  ensureEntranceObjects(visitor);\n\n  // ensure enter/exit callbacks are arrays\n  ensureCallbackArrays(visitor);\n\n  // add type wrappers\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (!isVirtualType(nodeType)) continue;\n\n    // wrap all the functions\n    const fns = visitor[nodeType];\n    for (const type of Object.keys(fns)) {\n      // @ts-expect-error normalised as VisitNodeObject\n      fns[type] = wrapCheck(nodeType, fns[type]);\n    }\n\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    const types = virtualTypes[nodeType];\n    if (types !== null) {\n      for (const type of types) {\n        // merge the visitor if necessary or just put it back in\n        if (visitor[type]) {\n          mergePair(visitor[type], fns);\n        } else {\n          // @ts-expect-error Expression produces too complex union\n          visitor[type] = fns;\n        }\n      }\n    } else {\n      mergePair(visitor, fns);\n    }\n  }\n\n  // add aliases\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    let aliases = FLIPPED_ALIAS_KEYS[nodeType];\n\n    if (nodeType in DEPRECATED_KEYS) {\n      const deprecatedKey = DEPRECATED_KEYS[nodeType];\n      deprecationWarning(nodeType, deprecatedKey, \"Visitor \");\n      aliases = [deprecatedKey];\n    } else if (nodeType in DEPRECATED_ALIASES) {\n      const deprecatedAlias =\n        DEPRECATED_ALIASES[nodeType as keyof typeof DEPRECATED_ALIASES];\n      deprecationWarning(nodeType, deprecatedAlias, \"Visitor \");\n      aliases = FLIPPED_ALIAS_KEYS[deprecatedAlias];\n    }\n\n    if (!aliases) continue;\n\n    const fns = visitor[nodeType];\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    for (const alias of aliases) {\n      const existing = visitor[alias];\n      if (existing) {\n        mergePair(existing, fns);\n      } else {\n        visitor[alias] = { ...fns };\n      }\n    }\n  }\n\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    ensureCallbackArrays(\n      // @ts-expect-error nodeType must present in visitor after previous validations\n      visitor[nodeType],\n    );\n  }\n\n  // @ts-expect-error explosion has been performed\n  return visitor as ExplodedVisitor;\n}\n\n// We need to name this function `verify$1` because otherwise rollup-plugin-dts\n// will generate a `namespace traverse { var verify: typeof verify; }` when\n// bundling @babel/traverse's index.d.ts.\n// TODO: Just call it `verify` once https://github.com/Swatinem/rollup-plugin-dts/issues/307\n// is fixed.\nexport { verify$1 as verify };\nfunction verify$1(visitor: Visitor) {\n  // @ts-expect-error _verified is not defined on non-verified Visitor.\n  // TODO: unify _verified and _exploded.\n  if (visitor._verified) return;\n\n  if (typeof visitor === \"function\") {\n    throw new Error(\n      \"You passed `traverse()` a function when it expected a visitor object, \" +\n        \"are you sure you didn't mean `{ enter: Function }`?\",\n    );\n  }\n\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (nodeType === \"enter\" || nodeType === \"exit\") {\n      validateVisitorMethods(nodeType, visitor[nodeType]);\n    }\n\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (!TYPES.includes(nodeType)) {\n      throw new Error(\n        `You gave us a visitor for the node type ${nodeType} but it's not a valid type in @babel/traverse ${PACKAGE_JSON.version}`,\n      );\n    }\n\n    const visitors = visitor[nodeType];\n    if (typeof visitors === \"object\") {\n      for (const visitorKey of Object.keys(visitors)) {\n        if (visitorKey === \"enter\" || visitorKey === \"exit\") {\n          // verify that it just contains functions\n          validateVisitorMethods(\n            `${nodeType}.${visitorKey}`,\n            visitors[visitorKey],\n          );\n        } else {\n          throw new Error(\n            \"You passed `traverse()` a visitor object with the property \" +\n              `${nodeType} that has the invalid property ${visitorKey}`,\n          );\n        }\n      }\n    }\n  }\n\n  // @ts-expect-error _verified is not defined on non-verified Visitor.\n  // TODO: unify _verified and _exploded.\n  visitor._verified = true;\n}\n\nfunction validateVisitorMethods(\n  path: string,\n  val: any,\n): asserts val is Function | Function[] {\n  const fns = [].concat(val);\n  for (const fn of fns) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(\n        `Non-function found defined in ${path} with type ${typeof fn}`,\n      );\n    }\n  }\n}\n\nexport function merge<State>(\n  visitors: Visitor<State>[],\n): ExplodedVisitor<State>;\nexport function merge(\n  visitors: Visitor<unknown>[],\n  states?: any[],\n  wrapper?: Function | null,\n): ExplodedVisitor<unknown>;\nexport function merge(\n  visitors: any[],\n  states: any[] = [],\n  wrapper?: VisitWrapper | null,\n): ExplodedVisitor {\n  const mergedVisitor: ExplodedVisitor = { _verified: true, _exploded: true };\n  if (!process.env.BABEL_8_BREAKING) {\n    // For compatibility with old Babel versions, we must hide _verified and _exploded.\n    // Otherwise, old versions of the validator will throw sayng that `true` is not\n    // a function, because it tries to validate it as a visitor.\n    Object.defineProperty(mergedVisitor, \"_exploded\", { enumerable: false });\n    Object.defineProperty(mergedVisitor, \"_verified\", { enumerable: false });\n  }\n\n  for (let i = 0; i < visitors.length; i++) {\n    const visitor = explode$1(visitors[i]);\n    const state = states[i];\n\n    let topVisitor: ExplVisitNode<unknown, Node> = visitor;\n    if (state || wrapper) {\n      topVisitor = wrapWithStateOrWrapper(topVisitor, state, wrapper);\n    }\n    mergePair(mergedVisitor, topVisitor);\n\n    for (const key of Object.keys(visitor) as (keyof ExplodedVisitor)[]) {\n      if (shouldIgnoreKey(key)) continue;\n\n      let typeVisitor = visitor[key];\n\n      // if we have state or wrapper then overload the callbacks to take it\n      if (state || wrapper) {\n        typeVisitor = wrapWithStateOrWrapper(typeVisitor, state, wrapper);\n      }\n\n      const nodeVisitor = (mergedVisitor[key] ||= {});\n      mergePair(nodeVisitor, typeVisitor);\n    }\n  }\n\n  return mergedVisitor;\n}\n\nfunction wrapWithStateOrWrapper<State>(\n  oldVisitor: ExplVisitNode<State, Node>,\n  state: State | null,\n  wrapper?: VisitWrapper<State> | null,\n): ExplVisitNode<State, Node> {\n  const newVisitor: ExplVisitNode<State, Node> = {};\n\n  for (const phase of [\"enter\", \"exit\"] as VisitPhase[]) {\n    let fns = oldVisitor[phase];\n\n    // not an enter/exit array of callbacks\n    if (!Array.isArray(fns)) continue;\n\n    fns = fns.map(function (fn) {\n      let newFn = fn;\n\n      if (state) {\n        newFn = function (path: NodePath) {\n          fn.call(state, path, state);\n        };\n      }\n\n      if (wrapper) {\n        // @ts-expect-error Fixme: actually PluginPass.key (aka pluginAlias)?\n        newFn = wrapper(state?.key, phase, newFn);\n      }\n\n      // Override toString in case this function is printed, we want to print the wrapped function, same as we do in `wrapCheck`\n      if (newFn !== fn) {\n        newFn.toString = () => fn.toString();\n      }\n\n      return newFn;\n    });\n\n    newVisitor[phase] = fns;\n  }\n\n  return newVisitor;\n}\n\nfunction ensureEntranceObjects(obj: Visitor) {\n  for (const key of Object.keys(obj) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(key)) continue;\n\n    const fns = obj[key];\n    if (typeof fns === \"function\") {\n      // @ts-expect-error: Expression produces a union type that is too complex to represent.\n      obj[key] = { enter: fns };\n    }\n  }\n}\n\nfunction ensureCallbackArrays(obj: Visitor) {\n  if (obj.enter && !Array.isArray(obj.enter)) obj.enter = [obj.enter];\n  if (obj.exit && !Array.isArray(obj.exit)) obj.exit = [obj.exit];\n}\n\nfunction wrapCheck(nodeType: VIRTUAL_TYPES, fn: Function) {\n  const fnKey = `is${nodeType}`;\n  // @ts-expect-error we know virtualTypesValidators will contain `fnKey`, but TS doesn't\n  const validator = virtualTypesValidators[fnKey];\n  const newFn = function (this: unknown, path: NodePath) {\n    if (validator.call(path)) {\n      return fn.apply(this, arguments);\n    }\n  };\n  newFn.toString = () => fn.toString();\n  return newFn;\n}\n\nfunction shouldIgnoreKey(key: string): key is\n  | `_${string}` // ` // Comment to fix syntax highlighting in vscode\n  | \"enter\"\n  | \"exit\"\n  | \"shouldSkip\"\n  | \"denylist\"\n  | \"noScope\"\n  | \"skipKeys\"\n  | \"blacklist\" {\n  // internal/hidden key\n  if (key[0] === \"_\") return true;\n\n  // ignore function keys\n  if (key === \"enter\" || key === \"exit\" || key === \"shouldSkip\") return true;\n\n  // ignore other options\n  if (key === \"denylist\" || key === \"noScope\" || key === \"skipKeys\") {\n    return true;\n  }\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (key === \"blacklist\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/*\nfunction mergePair(\n  dest: ExplVisitNode<unknown, Node>,\n  src: ExplVisitNode<unknown, Node>,\n);\n*/\nfunction mergePair(dest: any, src: any) {\n  for (const phase of [\"enter\", \"exit\"] as VisitPhase[]) {\n    if (!src[phase]) continue;\n    dest[phase] = [].concat(dest[phase] || [], src[phase]);\n  }\n}\n\n// environmentVisitor should be used when traversing the whole class and not for specific class elements/methods.\n// For perf reasons, the environmentVisitor might be traversed with `{ noScope: true }`, which means `path.scope` is undefined.\n// Avoid using `path.scope` here\nconst _environmentVisitor: Visitor = {\n  FunctionParent(path) {\n    // arrows are not skipped because they inherit the context.\n    if (path.isArrowFunctionExpression()) return;\n\n    path.skip();\n    if (path.isMethod()) {\n      if (\n        !process.env.BABEL_8_BREAKING &&\n        !path.requeueComputedKeyAndDecorators\n      ) {\n        // See https://github.com/babel/babel/issues/16694\n        requeueComputedKeyAndDecorators.call(path);\n      } else {\n        path.requeueComputedKeyAndDecorators();\n      }\n    }\n  },\n  Property(path) {\n    if (path.isObjectProperty()) return;\n    path.skip();\n    if (\n      !process.env.BABEL_8_BREAKING &&\n      !path.requeueComputedKeyAndDecorators\n    ) {\n      // See https://github.com/babel/babel/issues/16694\n      requeueComputedKeyAndDecorators.call(path);\n    } else {\n      path.requeueComputedKeyAndDecorators();\n    }\n  },\n};\n\nexport function environmentVisitor<S>(visitor: Visitor<S>): Visitor<S> {\n  return merge([_environmentVisitor, visitor]);\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AASA,IAAAG,QAAA,GAAAH,OAAA;AAAoE;EARlEI,eAAe;EACfC,kBAAkB;EAClBC,kBAAkB;EAClBC,KAAK;EACLC,8BAA8B,EAAIC;AAAkB,IAAAP,EAAA;AAOtD,SAASQ,aAAaA,CAACC,IAAY,EAAyB;EAC1D,OAAOA,IAAI,IAAIZ,YAAY;AAC7B;AAOO,SAASa,iBAAiBA,CAC/BC,OAAgB,EACY;EAE5B,OAAOA,OAAO,oBAAPA,OAAO,CAAEC,SAAS;AAC3B;AAuBA,SAASC,SAASA,CAAIF,OAAmB,EAAsB;EAC7D,IAAID,iBAAiB,CAACC,OAAO,CAAC,EAAE,OAAOA,OAAO;EAE9CA,OAAO,CAACC,SAAS,GAAG,IAAI;EAGxB,KAAK,MAAME,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAAuB;IAChE,IAAIM,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,MAAMI,KAAoB,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAChD,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAExB,MAAMC,GAAG,GAAGV,OAAO,CAACG,QAAQ,CAAC;IAC7B,OAAOH,OAAO,CAACG,QAAQ,CAAC;IAExB,KAAK,MAAMQ,IAAI,IAAIJ,KAAK,EAAE;MAExBP,OAAO,CAACW,IAAI,CAAC,GAAGD,GAAG;IACrB;EACF;EAGAE,QAAQ,CAACZ,OAAO,CAAC;EAKjB,OAAOA,OAAO,CAACa,UAAU;EAGzBC,qBAAqB,CAACd,OAAO,CAAC;EAG9Be,oBAAoB,CAACf,OAAO,CAAC;EAG7B,KAAK,MAAMG,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAAE;IAC3C,IAAIM,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAI,CAACN,aAAa,CAACM,QAAQ,CAAC,EAAE;IAG9B,MAAMO,GAAG,GAAGV,OAAO,CAACG,QAAQ,CAAC;IAC7B,KAAK,MAAML,IAAI,IAAIM,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,EAAE;MAEnCA,GAAG,CAACZ,IAAI,CAAC,GAAGkB,SAAS,CAACb,QAAQ,EAAEO,GAAG,CAACZ,IAAI,CAAC,CAAC;IAC5C;IAGA,OAAOE,OAAO,CAACG,QAAQ,CAAC;IAExB,MAAMc,KAAK,GAAG/B,YAAY,CAACiB,QAAQ,CAAC;IACpC,IAAIc,KAAK,KAAK,IAAI,EAAE;MAClB,KAAK,MAAMnB,IAAI,IAAImB,KAAK,EAAE;QAExB,IAAIjB,OAAO,CAACF,IAAI,CAAC,EAAE;UACjBoB,SAAS,CAAClB,OAAO,CAACF,IAAI,CAAC,EAAEY,GAAG,CAAC;QAC/B,CAAC,MAAM;UAELV,OAAO,CAACF,IAAI,CAAC,GAAGY,GAAG;QACrB;MACF;IACF,CAAC,MAAM;MACLQ,SAAS,CAAClB,OAAO,EAAEU,GAAG,CAAC;IACzB;EACF;EAGA,KAAK,MAAMP,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAAuB;IAChE,IAAIM,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAIgB,OAAO,GAAG1B,kBAAkB,CAACU,QAAQ,CAAC;IAE1C,IAAIA,QAAQ,IAAIZ,eAAe,EAAE;MAC/B,MAAM6B,aAAa,GAAG7B,eAAe,CAACY,QAAQ,CAAC;MAC/CP,kBAAkB,CAACO,QAAQ,EAAEiB,aAAa,EAAE,UAAU,CAAC;MACvDD,OAAO,GAAG,CAACC,aAAa,CAAC;IAC3B,CAAC,MAAM,IAAIjB,QAAQ,IAAIX,kBAAkB,EAAE;MACzC,MAAM6B,eAAe,GACnB7B,kBAAkB,CAACW,QAAQ,CAAoC;MACjEP,kBAAkB,CAACO,QAAQ,EAAEkB,eAAe,EAAE,UAAU,CAAC;MACzDF,OAAO,GAAG1B,kBAAkB,CAAC4B,eAAe,CAAC;IAC/C;IAEA,IAAI,CAACF,OAAO,EAAE;IAEd,MAAMT,GAAG,GAAGV,OAAO,CAACG,QAAQ,CAAC;IAE7B,OAAOH,OAAO,CAACG,QAAQ,CAAC;IAExB,KAAK,MAAMmB,KAAK,IAAIH,OAAO,EAAE;MAC3B,MAAMI,QAAQ,GAAGvB,OAAO,CAACsB,KAAK,CAAC;MAC/B,IAAIC,QAAQ,EAAE;QACZL,SAAS,CAACK,QAAQ,EAAEb,GAAG,CAAC;MAC1B,CAAC,MAAM;QACLV,OAAO,CAACsB,KAAK,CAAC,GAAAlB,MAAA,CAAAoB,MAAA,KAAQd,GAAG,CAAE;MAC7B;IACF;EACF;EAEA,KAAK,MAAMP,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAAE;IAC3C,IAAIM,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/BY,oBAAoB,CAElBf,OAAO,CAACG,QAAQ,CAClB,CAAC;EACH;EAGA,OAAOH,OAAO;AAChB;AAQA,SAASY,QAAQA,CAACZ,OAAgB,EAAE;EAGlC,IAAIA,OAAO,CAACyB,SAAS,EAAE;EAEvB,IAAI,OAAOzB,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAI0B,KAAK,CACb,wEAAwE,GACtE,qDACJ,CAAC;EACH;EAEA,KAAK,MAAMvB,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAAuB;IAChE,IAAIG,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,MAAM,EAAE;MAC/CwB,sBAAsB,CAACxB,QAAQ,EAAEH,OAAO,CAACG,QAAQ,CAAC,CAAC;IACrD;IAEA,IAAIG,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAI,CAACT,KAAK,CAACkC,QAAQ,CAACzB,QAAQ,CAAC,EAAE;MAC7B,MAAM,IAAIuB,KAAK,CACb,2CAA2CvB,QAAQ,2DACrD,CAAC;IACH;IAEA,MAAM0B,QAAQ,GAAG7B,OAAO,CAACG,QAAQ,CAAC;IAClC,IAAI,OAAO0B,QAAQ,KAAK,QAAQ,EAAE;MAChC,KAAK,MAAMC,UAAU,IAAI1B,MAAM,CAACC,IAAI,CAACwB,QAAQ,CAAC,EAAE;QAC9C,IAAIC,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,MAAM,EAAE;UAEnDH,sBAAsB,CACpB,GAAGxB,QAAQ,IAAI2B,UAAU,EAAE,EAC3BD,QAAQ,CAACC,UAAU,CACrB,CAAC;QACH,CAAC,MAAM;UACL,MAAM,IAAIJ,KAAK,CACb,6DAA6D,GAC3D,GAAGvB,QAAQ,kCAAkC2B,UAAU,EAC3D,CAAC;QACH;MACF;IACF;EACF;EAIA9B,OAAO,CAACyB,SAAS,GAAG,IAAI;AAC1B;AAEA,SAASE,sBAAsBA,CAC7BI,IAAY,EACZC,GAAQ,EAC8B;EACtC,MAAMtB,GAAG,GAAG,EAAE,CAACuB,MAAM,CAACD,GAAG,CAAC;EAC1B,KAAK,MAAME,EAAE,IAAIxB,GAAG,EAAE;IACpB,IAAI,OAAOwB,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIC,SAAS,CACjB,iCAAiCJ,IAAI,cAAc,OAAOG,EAAE,EAC9D,CAAC;IACH;EACF;AACF;AAUO,SAASE,KAAKA,CACnBP,QAAe,EACfQ,MAAa,GAAG,EAAE,EAClBC,OAA6B,EACZ;EACjB,MAAMC,aAA8B,GAAG;IAAEd,SAAS,EAAE,IAAI;IAAExB,SAAS,EAAE;EAAK,CAAC;EACxC;IAIjCG,MAAM,CAACoC,cAAc,CAACD,aAAa,EAAE,WAAW,EAAE;MAAEE,UAAU,EAAE;IAAM,CAAC,CAAC;IACxErC,MAAM,CAACoC,cAAc,CAACD,aAAa,EAAE,WAAW,EAAE;MAAEE,UAAU,EAAE;IAAM,CAAC,CAAC;EAC1E;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,QAAQ,CAACpB,MAAM,EAAEiC,CAAC,EAAE,EAAE;IACxC,MAAM1C,OAAO,GAAGE,SAAS,CAAC2B,QAAQ,CAACa,CAAC,CAAC,CAAC;IACtC,MAAMC,KAAK,GAAGN,MAAM,CAACK,CAAC,CAAC;IAEvB,IAAIE,UAAwC,GAAG5C,OAAO;IACtD,IAAI2C,KAAK,IAAIL,OAAO,EAAE;MACpBM,UAAU,GAAGC,sBAAsB,CAACD,UAAU,EAAED,KAAK,EAAEL,OAAO,CAAC;IACjE;IACApB,SAAS,CAACqB,aAAa,EAAEK,UAAU,CAAC;IAEpC,KAAK,MAAME,GAAG,IAAI1C,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,EAA+B;MACnE,IAAIM,eAAe,CAACwC,GAAG,CAAC,EAAE;MAE1B,IAAIC,WAAW,GAAG/C,OAAO,CAAC8C,GAAG,CAAC;MAG9B,IAAIH,KAAK,IAAIL,OAAO,EAAE;QACpBS,WAAW,GAAGF,sBAAsB,CAACE,WAAW,EAAEJ,KAAK,EAAEL,OAAO,CAAC;MACnE;MAEA,MAAMU,WAAW,GAAIT,aAAa,CAACO,GAAG,CAAC,KAAlBP,aAAa,CAACO,GAAG,CAAC,GAAK,CAAC,CAAC,CAAC;MAC/C5B,SAAS,CAAC8B,WAAW,EAAED,WAAW,CAAC;IACrC;EACF;EAEA,OAAOR,aAAa;AACtB;AAEA,SAASM,sBAAsBA,CAC7BI,UAAsC,EACtCN,KAAmB,EACnBL,OAAoC,EACR;EAC5B,MAAMY,UAAsC,GAAG,CAAC,CAAC;EAEjD,KAAK,MAAMC,KAAK,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAkB;IACrD,IAAIzC,GAAG,GAAGuC,UAAU,CAACE,KAAK,CAAC;IAG3B,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC3C,GAAG,CAAC,EAAE;IAEzBA,GAAG,GAAGA,GAAG,CAAC4C,GAAG,CAAC,UAAUpB,EAAE,EAAE;MAC1B,IAAIqB,KAAK,GAAGrB,EAAE;MAEd,IAAIS,KAAK,EAAE;QACTY,KAAK,GAAG,SAAAA,CAAUxB,IAAc,EAAE;UAChCG,EAAE,CAACsB,IAAI,CAACb,KAAK,EAAEZ,IAAI,EAAEY,KAAK,CAAC;QAC7B,CAAC;MACH;MAEA,IAAIL,OAAO,EAAE;QAEXiB,KAAK,GAAGjB,OAAO,CAACK,KAAK,oBAALA,KAAK,CAAEG,GAAG,EAAEK,KAAK,EAAEI,KAAK,CAAC;MAC3C;MAGA,IAAIA,KAAK,KAAKrB,EAAE,EAAE;QAChBqB,KAAK,CAACE,QAAQ,GAAG,MAAMvB,EAAE,CAACuB,QAAQ,CAAC,CAAC;MACtC;MAEA,OAAOF,KAAK;IACd,CAAC,CAAC;IAEFL,UAAU,CAACC,KAAK,CAAC,GAAGzC,GAAG;EACzB;EAEA,OAAOwC,UAAU;AACnB;AAEA,SAASpC,qBAAqBA,CAAC4C,GAAY,EAAE;EAC3C,KAAK,MAAMZ,GAAG,IAAI1C,MAAM,CAACC,IAAI,CAACqD,GAAG,CAAC,EAAuB;IACvD,IAAIpD,eAAe,CAACwC,GAAG,CAAC,EAAE;IAE1B,MAAMpC,GAAG,GAAGgD,GAAG,CAACZ,GAAG,CAAC;IACpB,IAAI,OAAOpC,GAAG,KAAK,UAAU,EAAE;MAE7BgD,GAAG,CAACZ,GAAG,CAAC,GAAG;QAAEa,KAAK,EAAEjD;MAAI,CAAC;IAC3B;EACF;AACF;AAEA,SAASK,oBAAoBA,CAAC2C,GAAY,EAAE;EAC1C,IAAIA,GAAG,CAACC,KAAK,IAAI,CAACP,KAAK,CAACC,OAAO,CAACK,GAAG,CAACC,KAAK,CAAC,EAAED,GAAG,CAACC,KAAK,GAAG,CAACD,GAAG,CAACC,KAAK,CAAC;EACnE,IAAID,GAAG,CAACE,IAAI,IAAI,CAACR,KAAK,CAACC,OAAO,CAACK,GAAG,CAACE,IAAI,CAAC,EAAEF,GAAG,CAACE,IAAI,GAAG,CAACF,GAAG,CAACE,IAAI,CAAC;AACjE;AAEA,SAAS5C,SAASA,CAACb,QAAuB,EAAE+B,EAAY,EAAE;EACxD,MAAM2B,KAAK,GAAG,KAAK1D,QAAQ,EAAE;EAE7B,MAAM2D,SAAS,GAAG1E,sBAAsB,CAACyE,KAAK,CAAC;EAC/C,MAAMN,KAAK,GAAG,SAAAA,CAAyBxB,IAAc,EAAE;IACrD,IAAI+B,SAAS,CAACN,IAAI,CAACzB,IAAI,CAAC,EAAE;MACxB,OAAOG,EAAE,CAAC6B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClC;EACF,CAAC;EACDT,KAAK,CAACE,QAAQ,GAAG,MAAMvB,EAAE,CAACuB,QAAQ,CAAC,CAAC;EACpC,OAAOF,KAAK;AACd;AAEA,SAASjD,eAAeA,CAACwC,GAAW,EAQpB;EAEd,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;EAG/B,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,YAAY,EAAE,OAAO,IAAI;EAG1E,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,UAAU,EAAE;IACjE,OAAO,IAAI;EACb;EAEmC;IACjC,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvB,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAQA,SAAS5B,SAASA,CAAC+C,IAAS,EAAEC,GAAQ,EAAE;EACtC,KAAK,MAAMf,KAAK,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAkB;IACrD,IAAI,CAACe,GAAG,CAACf,KAAK,CAAC,EAAE;IACjBc,IAAI,CAACd,KAAK,CAAC,GAAG,EAAE,CAAClB,MAAM,CAACgC,IAAI,CAACd,KAAK,CAAC,IAAI,EAAE,EAAEe,GAAG,CAACf,KAAK,CAAC,CAAC;EACxD;AACF;AAKA,MAAMgB,mBAA4B,GAAG;EACnCC,cAAcA,CAACrC,IAAI,EAAE;IAEnB,IAAIA,IAAI,CAACsC,yBAAyB,CAAC,CAAC,EAAE;IAEtCtC,IAAI,CAACuC,IAAI,CAAC,CAAC;IACX,IAAIvC,IAAI,CAACwC,QAAQ,CAAC,CAAC,EAAE;MACnB,IAEE,CAACxC,IAAI,CAACyC,+BAA+B,EACrC;QAEAA,wCAA+B,CAAChB,IAAI,CAACzB,IAAI,CAAC;MAC5C,CAAC,MAAM;QACLA,IAAI,CAACyC,+BAA+B,CAAC,CAAC;MACxC;IACF;EACF,CAAC;EACDC,QAAQA,CAAC1C,IAAI,EAAE;IACb,IAAIA,IAAI,CAAC2C,gBAAgB,CAAC,CAAC,EAAE;IAC7B3C,IAAI,CAACuC,IAAI,CAAC,CAAC;IACX,IAEE,CAACvC,IAAI,CAACyC,+BAA+B,EACrC;MAEAA,wCAA+B,CAAChB,IAAI,CAACzB,IAAI,CAAC;IAC5C,CAAC,MAAM;MACLA,IAAI,CAACyC,+BAA+B,CAAC,CAAC;IACxC;EACF;AACF,CAAC;AAEM,SAASG,kBAAkBA,CAAI3E,OAAmB,EAAc;EACrE,OAAOoC,KAAK,CAAC,CAAC+B,mBAAmB,EAAEnE,OAAO,CAAC,CAAC;AAC9C", "ignoreList": []}