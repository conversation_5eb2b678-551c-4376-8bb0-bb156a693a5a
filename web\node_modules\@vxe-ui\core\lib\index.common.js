"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  use: true,
  getComponent: true,
  component: true,
  renderEmptyElement: true,
  VxeUI: true
};
exports.VxeUI = void 0;
exports.component = component;
exports.default = void 0;
exports.getComponent = getComponent;
exports.renderEmptyElement = renderEmptyElement;
exports.use = use;
var _core = require("./src/core");
Object.keys(_core).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _core[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _core[key];
    }
  });
});
var _vue = require("vue");
var _config = require("./src/config");
Object.keys(_config).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _config[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _config[key];
    }
  });
});
var _dataStore = require("./src/dataStore");
Object.keys(_dataStore).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _dataStore[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _dataStore[key];
    }
  });
});
var _icon = require("./src/icon");
Object.keys(_icon).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _icon[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _icon[key];
    }
  });
});
var _theme = require("./src/theme");
Object.keys(_theme).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _theme[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _theme[key];
    }
  });
});
var _event = require("./src/event");
Object.keys(_event).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _event[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _event[key];
    }
  });
});
var _resize = require("./src/resize");
Object.keys(_resize).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _resize[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _resize[key];
    }
  });
});
var _i18n = require("./src/i18n");
Object.keys(_i18n).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _i18n[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _i18n[key];
    }
  });
});
var _renderer = require("./src/renderer");
Object.keys(_renderer).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _renderer[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _renderer[key];
    }
  });
});
var _validators = require("./src/validators");
Object.keys(_validators).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _validators[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _validators[key];
    }
  });
});
var _menus = require("./src/menus");
Object.keys(_menus).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _menus[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _menus[key];
    }
  });
});
var _formats = require("./src/formats");
Object.keys(_formats).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _formats[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _formats[key];
    }
  });
});
var _commands = require("./src/commands");
Object.keys(_commands).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _commands[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _commands[key];
    }
  });
});
var _interceptor = require("./src/interceptor");
Object.keys(_interceptor).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _interceptor[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _interceptor[key];
    }
  });
});
var _clipboard = require("./src/clipboard");
Object.keys(_clipboard).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _clipboard[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _clipboard[key];
    }
  });
});
var _permission = require("./src/permission");
Object.keys(_permission).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _permission[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _permission[key];
    }
  });
});
var _log = require("./src/log");
Object.keys(_log).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _log[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _log[key];
    }
  });
});
var _hooks = require("./src/hooks");
Object.keys(_hooks).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _hooks[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _hooks[key];
    }
  });
});
var _useFns = require("./src/useFns");
Object.keys(_useFns).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _useFns[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _useFns[key];
    }
  });
});
var _xeUtils = _interopRequireDefault(require("xe-utils"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const installedPlugins = [];
function use(Plugin, options) {
  if (Plugin && Plugin.install) {
    if (installedPlugins.indexOf(Plugin) === -1) {
      Plugin.install(VxeUI, options);
      installedPlugins.push(Plugin);
    }
  }
  return VxeUI;
}
const components = {};
function getComponent(name) {
  return components[name] || null;
}
function component(comp) {
  if (comp && comp.name) {
    components[comp.name] = comp;
    components[_xeUtils.default.kebabCase(comp.name)] = comp;
  }
}
function renderEmptyElement() {
  return (0, _vue.createCommentVNode)();
}
const VxeUI = exports.VxeUI = Object.assign(_core.VxeCore, {
  renderEmptyElement,
  setTheme: _theme.setTheme,
  getTheme: _theme.getTheme,
  setConfig: _config.setConfig,
  getConfig: _config.getConfig,
  setIcon: _icon.setIcon,
  getIcon: _icon.getIcon,
  setLanguage: _i18n.setLanguage,
  hasLanguage: _i18n.hasLanguage,
  getLanguage: _i18n.getLanguage,
  setI18n: _i18n.setI18n,
  getI18n: _i18n.getI18n,
  globalEvents: _event.globalEvents,
  GLOBAL_EVENT_KEYS: _event.GLOBAL_EVENT_KEYS,
  createEvent: _event.createEvent,
  globalResize: _resize.globalResize,
  renderer: _renderer.renderer,
  validators: _validators.validators,
  menus: _menus.menus,
  formats: _formats.formats,
  commands: _commands.commands,
  interceptor: _interceptor.interceptor,
  clipboard: _clipboard.clipboard,
  log: _log.log,
  permission: _permission.permission,
  globalStore: _dataStore.globalStore,
  hooks: _hooks.hooks,
  component,
  getComponent,
  useFns: _useFns.useFns,
  use
});
(0, _theme.setTheme)();
var _default = exports.default = VxeUI;