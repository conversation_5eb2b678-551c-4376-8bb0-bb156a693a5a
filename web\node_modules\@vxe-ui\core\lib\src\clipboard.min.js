Object.defineProperty(exports,"__esModule",{value:!0}),exports.clipboard=void 0;var _xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let copyElem,clipStore={text:"",html:""};function handleText(e){var t;copyElem||((copyElem=document.createElement("textarea")).id="$VxeCopy",(t=copyElem.style).width="48px",t.height="24px",t.position="fixed",t.zIndex="0",t.left="-500px",t.top="-500px",document.body.appendChild(copyElem)),copyElem.value=e}let clipboard=exports.clipboard={getStore(){return clipStore},setStore(e){Object.assign(clipStore,e||{})},copy(e){let t=!1;try{var l=_xeUtils.default.toValueString(e);handleText(l),copyElem.select(),copyElem.setSelectionRange(0,copyElem.value.length),t=document.execCommand("copy"),copyElem.blur(),clipStore.text=l,clipStore.html=""}catch(e){}return t},getText(){return clipStore.text||""}};