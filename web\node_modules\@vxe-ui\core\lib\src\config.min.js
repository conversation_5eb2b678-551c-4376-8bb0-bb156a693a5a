Object.defineProperty(exports,"__esModule",{value:!0}),exports.getConfig=getConfig,exports.setConfig=setConfig;var _xeUtils=_interopRequireDefault(require("xe-utils")),_domZindex=_interopRequireDefault(require("dom-zindex")),_core=require("./core"),_configStore=require("./configStore"),_theme=require("./theme");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function setConfig(e){return e&&(e.zIndex&&_domZindex.default.setCurrent(e.zIndex),e.theme&&(0,_theme.setTheme)(e.theme),_xeUtils.default.merge(_configStore.globalConfigStore,e)),_core.VxeCore}function getConfig(e,t){return arguments.length?_xeUtils.default.get(_configStore.globalConfigStore,e,t):_configStore.globalConfigStore}