Object.defineProperty(exports,"__esModule",{value:!0}),exports.globalEvents=exports.createEvent=exports.GLOBAL_EVENT_KEYS=void 0;var _xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let GLOBAL_EVENT_KEYS=exports.GLOBAL_EVENT_KEYS={F2:"F2",ESCAPE:"Escape",ENTER:"Enter",TAB:"Tab",DELETE:"Delete",BACKSPACE:"Backspace",SPACEBAR:" ",CONTEXT_MENU:"ContextMenu",ARROW_UP:"ArrowUp",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown",Control:"Control",R:"R",P:"P",Z:"Z",X:"X",C:"C",V:"V",M:"M"},browse=_xeUtils.default.browse(),convertEventKeys={" ":"Spacebar",Apps:GLOBAL_EVENT_KEYS.CONTEXT_MENU,Del:GLOBAL_EVENT_KEYS.DELETE,Up:GLOBAL_EVENT_KEYS.ARROW_UP,Down:GLOBAL_EVENT_KEYS.ARROW_DOWN,Left:GLOBAL_EVENT_KEYS.ARROW_LEFT,Right:GLOBAL_EVENT_KEYS.ARROW_RIGHT},wheelName=browse.firefox?"DOMMouseScroll":"mousewheel",eventStore=[];function triggerEvent(r){let n=r.type===wheelName;eventStore.forEach(({type:e,cb:t})=>{r.cancelBubble||(e===r.type||n&&"mousewheel"===e)&&t(r)})}class VxeComponentEvent{constructor(e,t,r){Object.defineProperty(this,"$event",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"type",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"key",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:""}),(this.$event=e)&&(e.type&&(this.type=e.type),e.key&&(this.key=e.key),e.code)&&(this.code=e.code),Object.assign(this,t),_xeUtils.default.objectEach(r,(r,n)=>{if(_xeUtils.default.isFunction(r)){let e=null,t=!1;Object.defineProperty(this,n,{get(){return t||(t=!0,e=r()),e}})}else this[n]=r})}stopPropagation(){var e=this.$event;e&&e.stopPropagation()}preventDefault(){var e=this.$event;e&&e.preventDefault()}}let createEvent=(e,t,r)=>new VxeComponentEvent(e,t,r),globalEvents=(exports.createEvent=createEvent,exports.globalEvents={on(e,t,r){eventStore.push({comp:e,type:t,cb:r})},off(t,r){_xeUtils.default.remove(eventStore,e=>e.comp===t&&e.type===r)},hasKey(e,t){e=e.key;return t=t.toLowerCase(),!(!e||t!==e.toLowerCase()&&(!convertEventKeys[e]||convertEventKeys[e].toLowerCase()!==t))}});browse.isDoc&&(browse.msie||(window.addEventListener("copy",triggerEvent,!1),window.addEventListener("cut",triggerEvent,!1),window.addEventListener("paste",triggerEvent,!1)),document.addEventListener("keydown",triggerEvent,!1),document.addEventListener("contextmenu",triggerEvent,!1),window.addEventListener("mousedown",triggerEvent,!1),window.addEventListener("blur",triggerEvent,!1),window.addEventListener("resize",triggerEvent,!1),window.addEventListener(wheelName,_xeUtils.default.throttle(triggerEvent,100,{leading:!0,trailing:!1}),{passive:!0,capture:!1}));