Object.defineProperty(exports,"__esModule",{value:!0}),exports.formats=void 0;var _xeUtils=_interopRequireDefault(require("xe-utils")),_log=require("./log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}class VXEFormatsStore{constructor(){Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:{}})}mixin(e){return _xeUtils.default.each(e,(e,t)=>{this.add(t,e)}),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(r,e){var t=this.store[r];_xeUtils.default.isFunction(e)&&(_log.log.warn("vxe.error.delProp",["formats -> callback","cellFormatMethod"]),e={cellFormatMethod:e});let o=_xeUtils.default.keys(t);return _xeUtils.default.each(e,(e,t)=>{o.includes(t)&&_log.log.warn("vxe.error.coverProp",[r,t])}),this.store[r]=t?_xeUtils.default.merge(t,e):e,this}delete(e){delete this.store[e]}forEach(e){_xeUtils.default.objectEach(this.store,e)}}let formats=exports.formats=new VXEFormatsStore;Object.assign(formats,{_name:"Formats"});