Object.defineProperty(exports,"__esModule",{value:!0}),exports.getI18n=getI18n,exports.getLanguage=getLanguage,exports.hasLanguage=hasLanguage,exports.setI18n=setI18n,exports.setLanguage=setLanguage;var _xeUtils=_interopRequireDefault(require("xe-utils")),_core=require("./core"),_i18nStore=require("./i18nStore"),_configStore=require("./configStore");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let checkInstall=!1,cacheMaps={};function getI18n(e,n){var{langMaps:t,language:r}=_i18nStore.i18nConfigStore,a=_configStore.globalConfigStore.i18n;return a?""+(a(e,n)||""):(checkInstall||(t[r]||console.error(`[vxe core] 语言包未安装。Language not installed. https://${_core.VxeCore.uiVersion?"vxeui.com":"vxetable.cn"}/#/start/i18n`),checkInstall=!0),!n&&cacheMaps[e]?cacheMaps[e]:(a=_xeUtils.default.toFormatString(_xeUtils.default.get(t[r],e,e),n),n||(cacheMaps[e]=a),a))}function setLanguage(e){var n=_i18nStore.i18nConfigStore.language,e=e||"zh-CN";return n!==e&&(_i18nStore.i18nConfigStore.language=e,cacheMaps={}),_core.VxeCore}function setI18n(e,n){return _i18nStore.i18nConfigStore.langMaps[e]=Object.assign({},n),_core.VxeCore}function hasLanguage(e){var n=_i18nStore.i18nConfigStore.langMaps;return!!n[e]}function getLanguage(){var e=_i18nStore.i18nConfigStore.language;return e}