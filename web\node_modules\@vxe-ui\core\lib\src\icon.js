"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getIcon = getIcon;
exports.setIcon = setIcon;
var _xeUtils = _interopRequireDefault(require("xe-utils"));
var _core = require("./core");
var _iconStore = require("./iconStore");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function setIcon(options) {
  if (options) {
    Object.assign(_iconStore.iconConfigStore, options);
  }
  return _core.VxeCore;
}
function getIcon(key) {
  return arguments.length ? _xeUtils.default.get(_iconStore.iconConfigStore, key) : _iconStore.iconConfigStore;
}