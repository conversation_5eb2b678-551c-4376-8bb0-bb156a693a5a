Object.defineProperty(exports,"__esModule",{value:!0}),exports.getIcon=getIcon,exports.setIcon=setIcon;var _xeUtils=_interopRequireDefault(require("xe-utils")),_core=require("./core"),_iconStore=require("./iconStore");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function setIcon(e){return e&&Object.assign(_iconStore.iconConfigStore,e),_core.VxeCore}function getIcon(e){return arguments.length?_xeUtils.default.get(_iconStore.iconConfigStore,e):_iconStore.iconConfigStore}