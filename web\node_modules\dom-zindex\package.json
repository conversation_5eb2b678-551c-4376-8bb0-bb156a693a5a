{"name": "dom-zindex", "version": "1.0.6", "description": "Web common z-index style management", "files": ["dist", "es", "types"], "scripts": {"lib": "gulp build"}, "main": "dist/index.common.js", "module": "es/index.esm.js", "unpkg": "dist/index.umd.js", "jsdelivr": "dist/index.umd.js", "typings": "types/index.d.ts", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/runtime": "^7.4.4", "eslint": "^5.16.0", "eslint-config-standard": "^12.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.3", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "typescript": "^4.6.4", "markdown-doctest": "^1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/x-extends/dom-zindex.git"}, "keywords": ["z-index", "zindex"], "author": {"name": "<PERSON>", "email": "xu_liang<PERSON><PERSON>@163.com"}, "license": "MIT", "bugs": {"url": "https://github.com/x-extends/dom-zindex/issues"}, "homepage": "https://github.com/x-extends/dom-zindex/"}