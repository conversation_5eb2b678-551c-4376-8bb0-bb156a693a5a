// Gets the largest z-index on the page.
export function getMax(): number

/**
 * Set main z-index value
 * @param value
 */
export function setCurrent(value: number): number

/**
 * Get main current z-index
 */
export function getCurrent(): number

/**
 * Get main next z-index
 */
export function getNext(): number

/**
 * Set subordinate z-index value
 * @param value
 */
export function setSubCurrent(value: number): number

/**
 * Get subordinate current z-index
 */
export function getSubCurrent(): number

/**
 * Get subordinate next z-index
 */
export function getSubNext(): number

declare const DomZIndex: {
  getMax: typeof getMax
  setCurrent: typeof setCurrent
  getCurrent: typeof getCurrent
  getNext: typeof getNext
  setSubCurrent: typeof setSubCurrent
  getSubCurrent: typeof getSubCurrent
  getSubNext: typeof getSubNext
}

export default DomZIndex