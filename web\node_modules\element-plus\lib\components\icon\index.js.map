{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/icon/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Icon from './src/icon.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElIcon: SFCWithInstall<typeof Icon> = withInstall(Icon)\nexport default ElIcon\n\nexport * from './src/icon'\n"], "names": ["withInstall", "Icon"], "mappings": ";;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI;;;;;;"}