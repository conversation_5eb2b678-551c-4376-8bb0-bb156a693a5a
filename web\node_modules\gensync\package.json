{"name": "gens<PERSON>", "version": "1.0.0-beta.2", "license": "MIT", "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/loganfsmyth/gensync", "repository": {"type": "git", "url": "https://github.com/loganfsmyth/gensync.git"}, "scripts": {"test": "jest"}, "engines": {"node": ">=6.9.0"}, "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "flow-bin": "^0.71.0", "jest": "^22.4.3", "prettier": "^1.12.1"}}