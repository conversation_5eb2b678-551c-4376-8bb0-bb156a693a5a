# sourcemap-codec changelog

## 1.4.8

* Performance boost ([#80](https://github.com/<PERSON><PERSON>/sourcemap-codec/pull/80))

## 1.4.7

* Include .map files in package ([#73](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/issues/73))

## 1.4.6

* Use arrays instead of typed arrays ([#79](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/79))

## 1.4.5

* Handle overflow cases ([#78](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/78))

## 1.4.4

* Use Uint32Array, yikes ([#77](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/77))

## 1.4.3

* Use Uint16Array to prevent overflow ([#75](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/75))

## 1.4.2

* GO EVEN FASTER ([#74](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/74))

## 1.4.1

* GO FASTER ([#71](https://github.com/<PERSON>-<PERSON>/sourcemap-codec/pull/71))

## 1.4.0

* Add TypeScript declarations ([#70](https://github.com/Rich-<PERSON>/sourcemap-codec/pull/70))

## 1.3.1

* Update build process, expose `pkg.module`

## 1.3.0

* Update build process

## 1.2.1

* Add dist files to npm package

## 1.2.0

* Add ES6 build
* Update dependencies
* Add test coverage

## 1.1.0

* Fix bug with lines containing single-character segments
* Add tests

## 1.0.0

* First release
