{"version": 3, "file": "sourcemap-codec.umd.js", "sources": ["../src/sourcemap-codec.ts"], "sourcesContent": ["export type SourceMapSegment =\n\t| [number]\n\t| [number, number, number, number]\n\t| [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nconst charToInteger: { [charCode: number]: number } = {};\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n\nfor (let i = 0; i < chars.length; i++) {\n\tcharToInteger[chars.charCodeAt(i)] = i;\n}\n\nexport function decode(mappings: string): SourceMapMappings {\n\tconst decoded: SourceMapMappings = [];\n\tlet line: SourceMapLine = [];\n\tconst segment: SourceMapSegment = [\n\t\t0, // generated code column\n\t\t0, // source file index\n\t\t0, // source code line\n\t\t0, // source code column\n\t\t0, // name index\n\t];\n\n\tlet j = 0;\n\tfor (let i = 0, shift = 0, value = 0; i < mappings.length; i++) {\n\t\tconst c = mappings.charCodeAt(i);\n\n\t\tif (c === 44) { // \",\"\n\t\t\tsegmentify(line, segment, j);\n\t\t\tj = 0;\n\n\t\t} else if (c === 59) { // \";\"\n\t\t\tsegmentify(line, segment, j);\n\t\t\tj = 0;\n\t\t\tdecoded.push(line);\n\t\t\tline = [];\n\t\t\tsegment[0] = 0;\n\n\t\t} else {\n\t\t\tlet integer = charToInteger[c];\n\t\t\tif (integer === undefined) {\n\t\t\t\tthrow new Error('Invalid character (' + String.fromCharCode(c) + ')');\n\t\t\t}\n\n\t\t\tconst hasContinuationBit = integer & 32;\n\n\t\t\tinteger &= 31;\n\t\t\tvalue += integer << shift;\n\n\t\t\tif (hasContinuationBit) {\n\t\t\t\tshift += 5;\n\t\t\t} else {\n\t\t\t\tconst shouldNegate = value & 1;\n\t\t\t\tvalue >>>= 1;\n\n\t\t\t\tif (shouldNegate) {\n\t\t\t\t\tvalue = value === 0 ? -0x80000000 : -value;\n\t\t\t\t}\n\n\t\t\t\tsegment[j] += value;\n\t\t\t\tj++;\n\t\t\t\tvalue = shift = 0; // reset\n\t\t\t}\n\t\t}\n\t}\n\n\tsegmentify(line, segment, j);\n\tdecoded.push(line);\n\n\treturn decoded;\n}\n\nfunction segmentify(line: SourceMapSegment[], segment: SourceMapSegment, j: number) {\n\t// This looks ugly, but we're creating specialized arrays with a specific\n\t// length. This is much faster than creating a new array (which v8 expands to\n\t// a capacity of 17 after pushing the first item), or slicing out a subarray\n\t// (which is slow). Length 4 is assumed to be the most frequent, followed by\n\t// length 5 (since not everything will have an associated name), followed by\n\t// length 1 (it's probably rare for a source substring to not have an\n\t// associated segment data).\n\tif (j === 4) line.push([segment[0], segment[1], segment[2], segment[3]]);\n\telse if (j === 5) line.push([segment[0], segment[1], segment[2], segment[3], segment[4]]);\n\telse if (j === 1) line.push([segment[0]]);\n}\n\nexport function encode(decoded: SourceMapMappings): string {\n\tlet sourceFileIndex = 0;  // second field\n\tlet sourceCodeLine = 0;   // third field\n\tlet sourceCodeColumn = 0; // fourth field\n\tlet nameIndex = 0;        // fifth field\n\tlet mappings = '';\n\n\tfor (let i = 0; i < decoded.length; i++) {\n\t\tconst line = decoded[i];\n\t\tif (i > 0) mappings += ';';\n\t\tif (line.length === 0) continue;\n\n\t\tlet generatedCodeColumn = 0; // first field\n\n\t\tconst lineMappings: string[] = [];\n\n\t\tfor (const segment of line) {\n\t\t\tlet segmentMappings = encodeInteger(segment[0] - generatedCodeColumn);\n\t\t\tgeneratedCodeColumn = segment[0];\n\n\t\t\tif (segment.length > 1) {\n\t\t\t\tsegmentMappings +=\n\t\t\t\t\tencodeInteger(segment[1] - sourceFileIndex) +\n\t\t\t\t\tencodeInteger(segment[2] - sourceCodeLine) +\n\t\t\t\t\tencodeInteger(segment[3] - sourceCodeColumn);\n\n\t\t\t\tsourceFileIndex = segment[1];\n\t\t\t\tsourceCodeLine = segment[2];\n\t\t\t\tsourceCodeColumn = segment[3];\n\t\t\t}\n\n\t\t\tif (segment.length === 5) {\n\t\t\t\tsegmentMappings += encodeInteger(segment[4] - nameIndex);\n\t\t\t\tnameIndex = segment[4];\n\t\t\t}\n\n\t\t\tlineMappings.push(segmentMappings);\n\t\t}\n\n\t\tmappings += lineMappings.join(',');\n\t}\n\n\treturn mappings;\n}\n\nfunction encodeInteger(num: number): string {\n\tvar result = '';\n\tnum = num < 0 ? (-num << 1) | 1 : num << 1;\n\tdo {\n\t\tvar clamped = num & 31;\n\t\tnum >>>= 5;\n\t\tif (num > 0) {\n\t\t\tclamped |= 32;\n\t\t}\n\t\tresult += chars[clamped];\n\t} while (num > 0);\n\n\treturn result;\n}\n"], "names": [], "mappings": ";;;;;;CAOA,IAAM,aAAa,GAAmC,EAAE,CAAC;CACzD,IAAM,KAAK,GAAG,mEAAmE,CAAC;CAElF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;KACtC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvC;AAED,UAAgB,MAAM,CAAC,QAAgB;KACtC,IAAM,OAAO,GAAsB,EAAE,CAAC;KACtC,IAAI,IAAI,GAAkB,EAAE,CAAC;KAC7B,IAAM,OAAO,GAAqB;SACjC,CAAC;SACD,CAAC;SACD,CAAC;SACD,CAAC;SACD,CAAC;MACD,CAAC;KAEF,IAAI,CAAC,GAAG,CAAC,CAAC;KACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;SAC/D,IAAM,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAEjC,IAAI,CAAC,KAAK,EAAE,EAAE;aACb,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aAC7B,CAAC,GAAG,CAAC,CAAC;UAEN;cAAM,IAAI,CAAC,KAAK,EAAE,EAAE;aACpB,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aAC7B,CAAC,GAAG,CAAC,CAAC;aACN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnB,IAAI,GAAG,EAAE,CAAC;aACV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAEf;cAAM;aACN,IAAI,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;aAC/B,IAAI,OAAO,KAAK,SAAS,EAAE;iBAC1B,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACtE;aAED,IAAM,kBAAkB,GAAG,OAAO,GAAG,EAAE,CAAC;aAExC,OAAO,IAAI,EAAE,CAAC;aACd,KAAK,IAAI,OAAO,IAAI,KAAK,CAAC;aAE1B,IAAI,kBAAkB,EAAE;iBACvB,KAAK,IAAI,CAAC,CAAC;cACX;kBAAM;iBACN,IAAM,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;iBAC/B,KAAK,MAAM,CAAC,CAAC;iBAEb,IAAI,YAAY,EAAE;qBACjB,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC;kBAC3C;iBAED,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;iBACpB,CAAC,EAAE,CAAC;iBACJ,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;cAClB;UACD;MACD;KAED,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC7B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAEnB,OAAO,OAAO,CAAC;CAChB,CAAC;CAED,SAAS,UAAU,CAAC,IAAwB,EAAE,OAAyB,EAAE,CAAS;;;;;;;;KAQjF,IAAI,CAAC,KAAK,CAAC;SAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpE,IAAI,CAAC,KAAK,CAAC;SAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrF,IAAI,CAAC,KAAK,CAAC;SAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3C,CAAC;AAED,UAAgB,MAAM,CAAC,OAA0B;KAChD,IAAI,eAAe,GAAG,CAAC,CAAC;KACxB,IAAI,cAAc,GAAG,CAAC,CAAC;KACvB,IAAI,gBAAgB,GAAG,CAAC,CAAC;KACzB,IAAI,SAAS,GAAG,CAAC,CAAC;KAClB,IAAI,QAAQ,GAAG,EAAE,CAAC;KAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;SACxC,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;SACxB,IAAI,CAAC,GAAG,CAAC;aAAE,QAAQ,IAAI,GAAG,CAAC;SAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;aAAE,SAAS;SAEhC,IAAI,mBAAmB,GAAG,CAAC,CAAC;SAE5B,IAAM,YAAY,GAAa,EAAE,CAAC;SAElC,KAAsB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;aAAvB,IAAM,OAAO,aAAA;aACjB,IAAI,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC;aACtE,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;aAEjC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;iBACvB,eAAe;qBACd,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;yBAC3C,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;yBAC1C,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC;iBAE9C,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC7B,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC5B,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;cAC9B;aAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;iBACzB,eAAe,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;iBACzD,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;cACvB;aAED,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;UACnC;SAED,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MACnC;KAED,OAAO,QAAQ,CAAC;CACjB,CAAC;CAED,SAAS,aAAa,CAAC,GAAW;KACjC,IAAI,MAAM,GAAG,EAAE,CAAC;KAChB,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;KAC3C,GAAG;SACF,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC;SACvB,GAAG,MAAM,CAAC,CAAC;SACX,IAAI,GAAG,GAAG,CAAC,EAAE;aACZ,OAAO,IAAI,EAAE,CAAC;UACd;SACD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;MACzB,QAAQ,GAAG,GAAG,CAAC,EAAE;KAElB,OAAO,MAAM,CAAC;CACf,CAAC;;;;;;;;;;;;;"}